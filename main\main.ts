import { app, BrowserWindow, ipc<PERSON>ain, dialog, screen } from "electron";
import path from "node:path";
import fs from "node:fs";
import started from "electron-squirrel-startup";

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
    },
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(
      path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`)
    );
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", createWindow);

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.

// 创建截图窗口
let captureWindow: BrowserWindow | null = null;

ipcMain.handle("start-screen-capture", async () => {
  try {
    // 1. 获取屏幕宽高
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    // captureWindow = new BrowserWindow({
    //   width: width,
    //   height: height,
    //   webPreferences: {
    //     preload: path.join(__dirname, "preload.js"),
    //   },
    //   frame: false,
    //   // transparent: true,
    //   // alwaysOnTop: true,
    //   // skipTaskbar: true,
    //   show: true,
    // });
    console.log("开始截图功能");
    // 返回成功状态
    return { success: true };
  } catch (error) {
    console.error("创建截图窗口失败:", error);
    return { success: false, error: error.message };
  }
});

// 保存截图处理器
ipcMain.handle('save-screen-capture', async (_event, dataUrl: string) => {
  try {
    // 这里添加保存截图的逻辑
    console.log("保存截图:", dataUrl.substring(0, 50) + "...");
    return "screenshot_saved_path.png";
  } catch (error) {
    console.error("保存截图失败:", error);
    return null;
  }
});

// 截取屏幕区域处理器
ipcMain.handle('capture-screen-area', async (_event, bounds: {x: number, y: number, width: number, height: number}) => {
  try {
    // 这里添加截取指定区域的逻辑
    console.log("截取屏幕区域:", bounds);
    return "area_screenshot_path.png";
  } catch (error) {
    console.error("截取屏幕区域失败:", error);
    return null;
  }
});
